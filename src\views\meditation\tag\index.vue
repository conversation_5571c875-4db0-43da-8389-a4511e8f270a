<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";
import {
  getMeditationTagList,
  createMeditationTag,
  updateMeditationTag,
  deleteMeditationTag,
  batchDeleteMeditationTags,
  type MeditationTag,
  type MeditationTagListParams,
  type CreateMeditationTagParams,
  type UpdateMeditationTagParams
} from "@/api/meditation";

defineOptions({
  name: "MeditationTag"
});

const loading = ref(false);
const tableData = ref<MeditationTag[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);
const detailDialogVisible = ref(false);
const currentRow = ref<MeditationTag | null>(null);
const selectedRows = ref<MeditationTag[]>([]);

const formData = reactive({
  id: "",
  name: "",
  is_enabled: true
});

const searchForm = reactive({
  search: ""
});

const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

const columns = [
  {
    type: "selection",
    width: 55,
    align: "left",
    hide: false
  },
  {
    label: "标签名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "状态",
    prop: "is_enabled",
    minWidth: 100,
    slot: "status"
  },
  {
    label: "使用次数",
    prop: "usage_count",
    minWidth: 100
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
];

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    const params: MeditationTagListParams = {
      page: pagination.currentPage,
      limit: pagination.pageSize,
      search: searchForm.search || undefined
    };

    const response = await getMeditationTagList(params);
    if (response.code === 200) {
      tableData.value = response.data.items;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.message || "获取数据失败");
    }
  } catch (error) {
    console.error("获取标签列表失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const onSearch = () => {
  pagination.currentPage = 1;
  fetchData();
};

const resetForm = () => {
  searchForm.search = "";
  onSearch();
};

const handleAdd = () => {
  dialogTitle.value = "新增标签";
  isEdit.value = false;
  resetFormData();
  dialogVisible.value = true;
};

const handleDetail = (row: MeditationTag) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

const handleEdit = (row: MeditationTag) => {
  dialogTitle.value = "编辑标签";
  isEdit.value = true;
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    is_enabled: row.is_enabled
  });
  dialogVisible.value = true;
};

const handleDelete = async (row: MeditationTag) => {
  try {
    await ElMessageBox.confirm(`确定要删除标签 ${row.name} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    const response = await deleteMeditationTag(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      fetchData();
    } else {
      ElMessage.error(response.message || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除标签失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请选择要删除的标签");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个标签吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const tagIds = selectedRows.value.map(row => parseInt(row.id));
    const response = await batchDeleteMeditationTags({ tag_ids: tagIds });
    if (response.code === 200) {
      ElMessage.success("批量删除成功");
      selectedRows.value = [];
      fetchData();
    } else {
      ElMessage.error(response.message || "批量删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("批量删除标签失败:", error);
      ElMessage.error("批量删除失败");
    }
  }
};

const resetFormData = () => {
  formData.id = "";
  formData.name = "";
  formData.is_enabled = true;
};

const handleSubmit = async () => {
  if (!formData.name) {
    ElMessage.warning("请输入标签名称");
    return;
  }

  try {
    if (isEdit.value) {
      const data: UpdateMeditationTagParams = {
        name: formData.name,
        is_enabled: formData.is_enabled
      };
      const response = await updateMeditationTag(formData.id, data);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        fetchData();
      } else {
        ElMessage.error(response.message || "编辑失败");
      }
    } else {
      const data: CreateMeditationTagParams = {
        name: formData.name,
        is_enabled: formData.is_enabled
      };
      const response = await createMeditationTag(data);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        fetchData();
      } else {
        ElMessage.error(response.message || "新增失败");
      }
    }
  } catch (error) {
    console.error("提交标签失败:", error);
    ElMessage.error("操作失败");
  }
};

// 表格选择处理
const handleSelectionChange = (selection: MeditationTag[]) => {
  selectedRows.value = selection;
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  fetchData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  fetchData();
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="搜索：" prop="search">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入标签名称"
          clearable
          class="!w-[200px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="标签管理" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增标签
        </el-button>
        <el-button
          type="danger"
          :icon="useRenderIcon('ep:delete')"
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <template #status="{ row }">
            <el-tag :type="row.is_enabled ? 'success' : 'danger'">
              {{ row.is_enabled ? "启用" : "禁用" }}
            </el-tag>
          </template>

          <template #created_at="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>

          <template #operation="{ row }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      draggable
    >
      <el-form :model="formData" label-width="100px" label-position="right">
        <el-form-item label="标签名称" required>
          <el-input
            v-model="formData.name"
            placeholder="请输入标签名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="formData.is_enabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />
  </div>
</template>

<style scoped lang="scss">
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
