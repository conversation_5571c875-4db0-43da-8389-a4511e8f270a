<template>
  <el-dialog
    v-model="visible"
    title="标签详情"
    width="600px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="标签名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.is_enabled ? 'success' : 'danger'">
            {{ data.is_enabled ? "启用" : "禁用" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="使用次数">
          {{ data.usage_count || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ new Date(data.created_at).toLocaleString() }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { MeditationTag } from "@/api/meditation";

interface Tag extends MeditationTag {
  // 扩展字段，如果需要的话
}

interface Props {
  modelValue: boolean;
  data?: Tag | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, val => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}
</style>
